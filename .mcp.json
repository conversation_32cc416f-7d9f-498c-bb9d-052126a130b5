{"mcpServers": {"playwright": {"type": "stdio", "command": "npx", "args": ["-y", "@playwright/mcp@latest", "--user-data-dir", "/Users/<USER>/Library/Caches/ms-playwright/mcp-chrome-profile"], "env": {}}, "nocobase": {"type": "stdio", "command": "node", "args": ["/Users/<USER>/Development/dev/mcp-nocobase/dist/index.js"], "env": {"NOCOBASE_BASE_URL": "http://nocobase.nocobasedocker.orb.local/api", "NOCOBASE_TOKEN": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw", "NOCOBASE_APP": "mcp_playground"}}}}