#!/usr/bin/env node

/**
 * 直接测试schema创建，模仿成功路由的方式
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

function generateUID(prefix = 'uid') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `${prefix}-${timestamp}-${random}`;
}

async function testDirectSchemaCreation() {
  console.log('🧪 直接测试schema创建\n');

  try {
    // 生成UIDs
    const pageSchemaUid = generateUID("page");
    const tabSchemaUid = generateUID("tab");
    const tabSchemaName = generateUID("tab");
    const menuSchemaUid = generateUID("menu");

    console.log('📋 生成的UIDs:');
    console.log(`   Page UID: ${pageSchemaUid}`);
    console.log(`   Tab UID: ${tabSchemaUid}`);
    console.log(`   Tab Name: ${tabSchemaName}`);
    console.log(`   Menu UID: ${menuSchemaUid}`);

    // 第一步：创建简单的Page schema（模仿成功路由）
    console.log('\n📋 Step 1: 创建Page schema');
    const pageSchema = {
      type: "void",
      "x-component": "Page",
      "x-async": false,
      name: `schema-${Date.now()}`,
      "x-uid": pageSchemaUid
    };

    console.log('🔧 Page schema:', JSON.stringify(pageSchema, null, 2));

    const pageResponse = await client.post('/uiSchemas:create', {
      values: pageSchema
    });

    console.log('✅ Page schema created:', JSON.stringify(pageResponse.data, null, 2));

    // 获取实际的Page schema UID
    const actualPageUid = pageResponse.data.data['x-uid'];
    console.log(`🔧 实际的Page UID: ${actualPageUid}`);

    // 第二步：创建Grid schema（模仿成功路由的tabs schema）
    console.log('\n📋 Step 2: 创建Grid schema');
    const gridSchema = {
      type: "void",
      "x-component": "Grid",
      "x-initializer": "page:addBlock",
      "x-index": 1,
      name: tabSchemaName,
      "x-uid": tabSchemaUid,
      "x-async": true
    };

    console.log('🔧 Grid schema:', JSON.stringify(gridSchema, null, 2));

    const gridResponse = await client.post('/uiSchemas:create', {
      values: gridSchema
    });

    console.log('✅ Grid schema created:', JSON.stringify(gridResponse.data, null, 2));

    // 获取实际的Grid schema UID
    const actualTabUid = gridResponse.data.data['x-uid'];
    console.log(`🔧 实际的Tab UID: ${actualTabUid}`);

    // 第三步：验证创建的schemas
    console.log('\n📋 Step 3: 验证创建的schemas');

    // 验证Page schema（使用实际的UID）
    try {
      const pageVerifyResponse = await client.get(`/uiSchemas:getJsonSchema/${actualPageUid}`);
      const pageVerifySchema = pageVerifyResponse.data.data;
      console.log('📊 验证Page schema:');
      console.log(JSON.stringify(pageVerifySchema, null, 2));
      
      const pageValid = pageVerifySchema.type === 'void' && 
                       pageVerifySchema['x-component'] === 'Page';
      console.log(`   Page schema有效: ${pageValid ? '✅' : '❌'}`);
    } catch (error) {
      console.log(`❌ 无法验证Page schema: ${error.message}`);
    }

    // 验证Grid schema（使用实际的UID）
    try {
      const gridVerifyResponse = await client.get(`/uiSchemas:getJsonSchema/${actualTabUid}`);
      const gridVerifySchema = gridVerifyResponse.data.data;
      console.log('📊 验证Grid schema:');
      console.log(JSON.stringify(gridVerifySchema, null, 2));
      
      // 处理可能被包装的schema
      let actualGridSchema = gridVerifySchema;
      if (gridVerifySchema.values && typeof gridVerifySchema.values === 'object') {
        console.log('🔧 Grid schema被包装在values中');
        actualGridSchema = gridVerifySchema.values;
      }
      
      const gridValid = actualGridSchema.type === 'void' && 
                       actualGridSchema['x-component'] === 'Grid' &&
                       actualGridSchema['x-initializer'] === 'page:addBlock';
      console.log(`   Grid schema有效: ${gridValid ? '✅' : '❌'}`);
      console.log(`   组件: ${actualGridSchema['x-component']}`);
      console.log(`   初始化器: ${actualGridSchema['x-initializer']}`);
      
      if (gridValid) {
        console.log('\n🎉 Schema创建成功！现在可以创建路由了');
        
        // 第四步：创建路由
        console.log('\n📋 Step 4: 创建路由');
        
        // 创建主路由（使用实际的UID）
        const routeData = {
          type: 'page',
          title: `直接测试页面 ${Date.now()}`,
          schemaUid: actualPageUid,  // 使用实际的UID
          menuSchemaUid: menuSchemaUid,
          tabSchemaName: tabSchemaName,
          enableTabs: false,
          hidden: false,
          sort: 10
        };

        const routeResponse = await client.post('/desktopRoutes:create', routeData);
        const mainRoute = routeResponse.data.data;
        console.log('✅ 主路由创建成功:', JSON.stringify(mainRoute, null, 2));

        // 创建tabs子路由（使用实际的UID）
        const tabsRouteData = {
          type: 'tabs',
          parentId: mainRoute.id,
          schemaUid: actualTabUid,  // 使用实际的UID
          tabSchemaName: tabSchemaName,
          hidden: true,
          sort: 1
        };

        const tabsRouteResponse = await client.post('/desktopRoutes:create', tabsRouteData);
        const tabsRoute = tabsRouteResponse.data.data;
        console.log('✅ Tabs路由创建成功:', JSON.stringify(tabsRoute, null, 2));

        // 生成访问链接（使用实际的UID）
        const pageUrl = `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${actualPageUid}`;
        console.log(`\n🌐 访问页面: ${pageUrl}`);
        console.log('\n🎯 如果这个页面能正常显示"Add block"按钮，说明修复成功！');
        
      } else {
        console.log('\n❌ Grid schema创建失败');
      }
      
    } catch (error) {
      console.log(`❌ 无法验证Grid schema: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testDirectSchemaCreation().catch(console.error);
