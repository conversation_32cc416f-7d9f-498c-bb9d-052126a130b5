#!/usr/bin/env node

/**
 * 检查特定的路由ID
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function checkSpecificRoutes() {
  console.log('🔍 检查特定的路由ID\n');

  // 检查我们创建的路由ID
  const routeIds = [37, 38, 39, 40];

  for (const routeId of routeIds) {
    console.log(`📋 检查路由 ID: ${routeId}`);
    
    try {
      // 尝试获取路由信息
      const routeResponse = await client.get(`/desktopRoutes:list?filter[id]=${routeId}`);
      const routes = routeResponse.data.data;
      
      if (routes.length > 0) {
        const route = routes[0];
        console.log(`✅ 找到路由 ID ${routeId}:`);
        console.log(`   Title: ${route.title || 'null'}`);
        console.log(`   Type: ${route.type}`);
        console.log(`   Schema UID: ${route.schemaUid || 'null'}`);
        console.log(`   Parent ID: ${route.parentId || 'null'}`);
        
        // 检查schema
        if (route.schemaUid) {
          try {
            const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${route.schemaUid}`);
            const schema = schemaResponse.data.data;
            
            if (Object.keys(schema).length === 0) {
              console.log(`   Schema: ❌ 空对象`);
            } else {
              console.log(`   Schema: ✅ 有内容`);
              console.log(`   组件: ${schema['x-component']}`);
              console.log(`   初始化器: ${schema['x-initializer'] || 'none'}`);
              
              // 如果是Grid组件，检查是否有正确的初始化器
              if (schema['x-component'] === 'Grid' && schema['x-initializer'] === 'page:addBlock') {
                console.log(`   🎉 这是正确的Grid组件！`);
              }
            }
          } catch (schemaError) {
            console.log(`   Schema: ❌ 无法获取 (${schemaError.message})`);
          }
        }
        
      } else {
        console.log(`❌ 路由 ID ${routeId} 不存在`);
      }
      
    } catch (error) {
      console.log(`❌ 无法检查路由 ID ${routeId}: ${error.message}`);
    }
    
    console.log(''); // 空行分隔
  }

  // 也检查一下总的路由数量
  try {
    const allRoutesResponse = await client.get('/desktopRoutes:list');
    const allRoutes = allRoutesResponse.data.data;
    console.log(`📊 总路由数量: ${allRoutes.length}`);
    
    // 显示最大的路由ID
    const maxId = Math.max(...allRoutes.map(route => route.id));
    console.log(`📊 最大路由ID: ${maxId}`);
    
    // 显示最新的几个路由
    const latestRoutes = allRoutes.sort((a, b) => b.id - a.id).slice(0, 10);
    console.log('\n📋 最新的10个路由:');
    latestRoutes.forEach(route => {
      console.log(`   - ID: ${route.id}, Title: "${route.title || 'null'}", Type: ${route.type}`);
    });
    
  } catch (error) {
    console.log(`❌ 无法获取路由列表: ${error.message}`);
  }
}

// 运行检查
checkSpecificRoutes().catch(console.error);
