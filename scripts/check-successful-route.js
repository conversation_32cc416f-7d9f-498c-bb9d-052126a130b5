#!/usr/bin/env node

/**
 * 检查成功路由的schema
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function checkSuccessfulRoute() {
  console.log('🔍 检查成功路由的schema\n');

  try {
    // 检查成功路由 ID 20
    const successfulRouteId = 20;
    const successfulTabsRouteId = 21;
    
    console.log(`📋 检查成功路由 (ID: ${successfulRouteId})`);
    
    // 直接使用已知的schema UID
    const route = {
      schemaUid: 'page-1755064624382-zkgaa3k57'  // 成功路由的schema UID
    };

    console.log('✅ 使用已知的成功路由schema UID:', route.schemaUid);
    
    // 检查主页面schema
    console.log('\n📋 检查主页面schema');
    try {
      const pageSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${route.schemaUid}`);
      const pageSchema = pageSchemaResponse.data.data;
      
      console.log('📊 主页面Schema:');
      console.log(JSON.stringify(pageSchema, null, 2));
      
      if (Object.keys(pageSchema).length === 0) {
        console.log('   ❌ 主页面schema是空的！');
      } else {
        console.log('   ✅ 主页面schema有内容');
      }
    } catch (error) {
      console.log(`   ❌ 无法获取主页面schema: ${error.message}`);
    }
    
    // 检查tabs schema
    console.log(`\n📋 检查tabs schema`);
    const tabsRoute = {
      schemaUid: 'tab-1755064624382-frtktglrl'  // 成功路由的tabs schema UID
    };

    console.log('✅ 使用已知的tabs schema UID:', tabsRoute.schemaUid);

    try {
      const tabsSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${tabsRoute.schemaUid}`);
      const tabsSchema = tabsSchemaResponse.data.data;
      
      console.log('📊 Tabs Schema:');
      console.log(JSON.stringify(tabsSchema, null, 2));
      
      if (Object.keys(tabsSchema).length === 0) {
        console.log('   ❌ Tabs schema是空的！');
      } else {
        console.log('   ✅ Tabs schema有内容');
        
        // 检查是否被包装在values中
        let actualSchema = tabsSchema;
        if (tabsSchema.values && typeof tabsSchema.values === 'object') {
          console.log('🔧 Schema被包装在values中');
          actualSchema = tabsSchema.values;
        }
        
        const isValid = actualSchema.type === 'void' && 
                       actualSchema['x-component'] === 'Grid' &&
                       actualSchema['x-initializer'] === 'page:addBlock';
        console.log(`   Schema有效: ${isValid ? '✅' : '❌'}`);
        console.log(`   组件: ${actualSchema['x-component']}`);
        console.log(`   初始化器: ${actualSchema['x-initializer']}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 无法获取tabs schema: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行检查
checkSuccessfulRoute().catch(console.error);
