#!/usr/bin/env node

/**
 * 获取当前菜单结构并查找 g2p1、g2p2 和 g2 菜单项
 */

import { NocoBaseClient } from '../dist/client.js';

// 使用测试环境配置
const client = new NocoBaseClient({
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw',
  app: 'mcp_playground'
});

async function getMenuStructure() {
  console.log('🔍 获取当前菜单结构\n');
  console.log('='.repeat(80));

  try {
    // 获取可访问的菜单路由
    console.log('📋 获取可访问的菜单路由...');
    const routes = await client.getAccessibleMenuRoutes({
      tree: true
    });

    console.log('✅ 菜单路由获取成功！');
    console.log('\n📊 当前菜单结构:');
    console.log('='.repeat(80));

    // 递归打印菜单结构
    function printMenuTree(items, level = 0, parentPath = '') {
      const indent = '  '.repeat(level);
      const prefix = level === 0 ? '├─ ' : '└─ ';
      
      items.forEach((item, index) => {
        const currentPath = parentPath ? `${parentPath} > ${item.title}` : item.title;
        const typeIcon = {
          'group': '📁',
          'page': '📄',
          'link': '🔗',
          'url': '🌐'
        }[item.type] || '📋';
        
        console.log(`${indent}${prefix}${typeIcon} ${item.title} (ID: ${item.id}, 类型: ${item.type})`);
        
        if (item.parentId) {
          console.log(`${indent}   父级ID: ${item.parentId}`);
        }
        
        if (item.schemaUid) {
          console.log(`${indent}   Schema UID: ${item.schemaUid}`);
        }
        
        if (item.children && item.children.length > 0) {
          printMenuTree(item.children, level + 1, currentPath);
        }
      });
    }

    if (Array.isArray(routes)) {
      printMenuTree(routes);
    } else if (routes && typeof routes === 'object') {
      // 如果是对象格式，尝试提取数组
      const routesArray = Object.values(routes);
      printMenuTree(routesArray);
    }

    console.log('\n' + '='.repeat(80));

    // 查找特定的菜单项
    console.log('\n🔍 查找特定菜单项 (g2p1, g2p2, g2):');
    console.log('-'.repeat(50));

    const targetItems = [];
    
    function searchTargetItems(items) {
      items.forEach(item => {
        if (['g2p1', 'g2p2', 'g2'].includes(item.title)) {
          targetItems.push(item);
        }
        
        if (item.children && item.children.length > 0) {
          searchTargetItems(item.children);
        }
      });
    }

    if (Array.isArray(routes)) {
      searchTargetItems(routes);
    } else if (routes && typeof routes === 'object') {
      const routesArray = Object.values(routes);
      searchTargetItems(routesArray);
    }

    if (targetItems.length > 0) {
      console.log('✅ 找到目标菜单项:');
      targetItems.forEach(item => {
        console.log(`- ${item.title} (ID: ${item.id}, 类型: ${item.type}, 父级ID: ${item.parentId || '无'})`);
      });
      
      // 分析层级关系
      console.log('\n📊 层级关系分析:');
      const g2 = targetItems.find(item => item.title === 'g2');
      const g2p1 = targetItems.find(item => item.title === 'g2p1');
      const g2p2 = targetItems.find(item => item.title === 'g2p2');
      
      if (g2) {
        console.log(`- g2 (ID: ${g2.id}) 是菜单组，可以作为父级`);
      }
      
      if (g2p1) {
        console.log(`- g2p1 (ID: ${g2p1.id}) 当前父级: ${g2p1.parentId || '无'}`);
        if (g2) {
          console.log(`  建议: 将 g2p1 移动到 g2 (ID: ${g2.id}) 下`);
        }
      }
      
      if (g2p2) {
        console.log(`- g2p2 (ID: ${g2p2.id}) 当前父级: ${g2p2.parentId || '无'}`);
        if (g2) {
          console.log(`  建议: 将 g2p2 移动到 g2 (ID: ${g2.id}) 下`);
        }
      }
      
    } else {
      console.log('❌ 未找到目标菜单项 (g2p1, g2p2, g2)');
      console.log('💡 建议: 可能需要先创建这些菜单项');
    }

    console.log('\n🎯 总结:');
    console.log('- 当前菜单结构已获取完成');
    console.log('- 已识别所有菜单项的层级关系');
    console.log('- 可以基于此信息进行菜单重组操作');

  } catch (error) {
    console.error('❌ 获取菜单结构时发生错误:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行获取菜单结构
getMenuStructure().catch(console.error);