#!/usr/bin/env node

/**
 * 分析成功的路由 "修复测试页面 1755064624380"
 * 了解它做对了什么
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function analyzeSuccessfulRoute() {
  console.log('🔍 分析成功的路由 "修复测试页面 1755064624380"\n');

  try {
    // 1. 获取所有路由，找到成功的那个
    console.log('📋 Step 1: 查找成功的路由');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    const successfulRoute = routes.find(route => 
      route.title === '修复测试页面 1755064624380'
    );

    if (!successfulRoute) {
      console.log('❌ 没有找到成功的路由');
      return;
    }

    console.log('✅ 找到成功的路由:');
    console.log(JSON.stringify(successfulRoute, null, 2));

    // 2. 分析路由的详细信息
    console.log('\n📋 Step 2: 分析路由详细信息');
    console.log(`   Route ID: ${successfulRoute.id}`);
    console.log(`   Title: ${successfulRoute.title}`);
    console.log(`   Type: ${successfulRoute.type}`);
    console.log(`   Schema UID: ${successfulRoute.schemaUid}`);
    console.log(`   Menu Schema UID: ${successfulRoute.menuSchemaUid}`);
    console.log(`   Tab Schema Name: ${successfulRoute.tabSchemaName}`);
    console.log(`   Enable Tabs: ${successfulRoute.enableTabs}`);

    // 3. 获取页面 schema
    console.log('\n📋 Step 3: 获取页面 Schema');
    const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${successfulRoute.schemaUid}`);
    const pageSchema = schemaResponse.data.data;

    console.log('📊 成功页面的完整 Schema:');
    console.log(JSON.stringify(pageSchema, null, 2));

    // 4. 分析 schema 结构
    console.log('\n📋 Step 4: 分析 Schema 结构');
    
    // 处理可能被包装的 schema
    let actualSchema = pageSchema;
    if (pageSchema.values && typeof pageSchema.values === 'object') {
      console.log('🔧 Schema 被包装在 values 中');
      actualSchema = pageSchema.values;
    }

    console.log('🔍 实际 Schema 分析:');
    console.log(`   类型: ${actualSchema.type}`);
    console.log(`   组件: ${actualSchema['x-component']}`);
    console.log(`   异步: ${actualSchema['x-async']}`);
    console.log(`   UID: ${actualSchema['x-uid']}`);
    console.log(`   名称: ${actualSchema.name}`);
    console.log(`   有属性: ${!!actualSchema.properties}`);
    
    if (actualSchema.properties) {
      console.log(`   属性数量: ${Object.keys(actualSchema.properties).length}`);
      console.log('   属性详情:');
      
      for (const [propName, propSchema] of Object.entries(actualSchema.properties)) {
        const prop = propSchema;
        console.log(`     - ${propName}:`);
        console.log(`       类型: ${prop.type}`);
        console.log(`       组件: ${prop['x-component']}`);
        console.log(`       初始化器: ${prop['x-initializer'] || 'none'}`);
        console.log(`       UID: ${prop['x-uid']}`);
        console.log(`       异步: ${prop['x-async']}`);
        console.log(`       索引: ${prop['x-index']}`);
        
        if (prop['x-component'] === 'Grid' && prop['x-initializer'] === 'page:addBlock') {
          console.log('       ✅ 这是关键的 Grid 组件！');
        }
      }
    }

    // 5. 检查关联的 tabs 路由
    console.log('\n📋 Step 5: 检查关联的 tabs 路由');
    const tabsRoute = routes.find(route => 
      route.parentId === successfulRoute.id && route.type === 'tabs'
    );

    if (tabsRoute) {
      console.log('✅ 找到关联的 tabs 路由:');
      console.log(JSON.stringify(tabsRoute, null, 2));

      // 获取 tabs schema
      if (tabsRoute.schemaUid) {
        try {
          const tabsSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${tabsRoute.schemaUid}`);
          const tabsSchema = tabsSchemaResponse.data.data;
          console.log('📊 Tabs Schema:');
          console.log(JSON.stringify(tabsSchema, null, 2));
        } catch (error) {
          console.log('⚠️ 无法获取 tabs schema:', error.message);
        }
      }
    } else {
      console.log('ℹ️ 没有找到关联的 tabs 路由');
    }

    // 6. 检查菜单 schema
    console.log('\n📋 Step 6: 检查菜单 Schema');
    if (successfulRoute.menuSchemaUid) {
      try {
        const menuSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${successfulRoute.menuSchemaUid}`);
        const menuSchema = menuSchemaResponse.data.data;
        console.log('📊 Menu Schema:');
        console.log(JSON.stringify(menuSchema, null, 2));
      } catch (error) {
        console.log('⚠️ 无法获取 menu schema:', error.message);
      }
    }

    // 7. 总结成功的关键因素
    console.log('\n🎯 成功的关键因素总结:');
    
    const hasValidPageSchema = actualSchema.type === 'void' && actualSchema['x-component'] === 'Page';
    const hasProperties = !!actualSchema.properties;
    const hasGridWithAddBlock = actualSchema.properties && 
      Object.values(actualSchema.properties).some(prop => 
        prop['x-component'] === 'Grid' && prop['x-initializer'] === 'page:addBlock'
      );

    console.log(`   ✅ 有效的 Page Schema: ${hasValidPageSchema ? '是' : '否'}`);
    console.log(`   ✅ 有 Properties: ${hasProperties ? '是' : '否'}`);
    console.log(`   ✅ 有 Grid 组件和 AddBlock: ${hasGridWithAddBlock ? '是' : '否'}`);
    console.log(`   ✅ Schema UID 映射: ${successfulRoute.schemaUid}`);
    console.log(`   ✅ Enable Tabs: ${successfulRoute.enableTabs}`);

    if (hasValidPageSchema && hasProperties && hasGridWithAddBlock) {
      console.log('\n🎉 这个路由成功的原因是具备了所有必要的组件！');
    } else {
      console.log('\n⚠️ 这个路由可能还有问题，需要进一步检查');
    }

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行分析
analyzeSuccessfulRoute().catch(console.error);
