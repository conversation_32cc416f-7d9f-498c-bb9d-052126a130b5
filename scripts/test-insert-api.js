#!/usr/bin/env node

/**
 * 测试使用 uiSchemas:insert API 创建schema
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

function generateUID(prefix = 'uid') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `${prefix}-${timestamp}-${random}`;
}

async function testInsertAPI() {
  console.log('🧪 测试使用 uiSchemas:insert API 创建schema\n');

  try {
    // 生成UIDs
    const pageSchemaUid = generateUID("page");
    const tabSchemaUid = generateUID("tab");
    const tabSchemaName = generateUID("tab");

    console.log('📋 生成的UIDs:');
    console.log(`   Page UID: ${pageSchemaUid}`);
    console.log(`   Tab UID: ${tabSchemaUid}`);
    console.log(`   Tab Name: ${tabSchemaName}`);

    // 第一步：使用 insert API 创建Page schema
    console.log('\n📋 Step 1: 使用 insert API 创建Page schema');
    const pageSchema = {
      type: "void",
      "x-component": "Page",
      "x-async": false,
      name: `schema-${Date.now()}`,
      "x-uid": pageSchemaUid
    };

    console.log('🔧 Page schema:', JSON.stringify(pageSchema, null, 2));

    const pageResponse = await client.post('/uiSchemas:insert', pageSchema);
    console.log('✅ Page schema created:', JSON.stringify(pageResponse.data, null, 2));

    // 第二步：使用 insert API 创建Grid schema
    console.log('\n📋 Step 2: 使用 insert API 创建Grid schema');
    const gridSchema = {
      type: "void",
      "x-component": "Grid",
      "x-initializer": "page:addBlock",
      "x-index": 1,
      name: tabSchemaName,
      "x-uid": tabSchemaUid,
      "x-async": true
    };

    console.log('🔧 Grid schema:', JSON.stringify(gridSchema, null, 2));

    const gridResponse = await client.post('/uiSchemas:insert', gridSchema);
    console.log('✅ Grid schema created:', JSON.stringify(gridResponse.data, null, 2));

    // 第三步：验证创建的schemas
    console.log('\n📋 Step 3: 验证创建的schemas');

    // 验证Page schema
    try {
      const pageVerifyResponse = await client.get(`/uiSchemas:getJsonSchema/${pageSchemaUid}`);
      const pageVerifySchema = pageVerifyResponse.data.data;
      console.log('📊 验证Page schema:');
      console.log(JSON.stringify(pageVerifySchema, null, 2));
      
      const pageValid = pageVerifySchema.type === 'void' && 
                       pageVerifySchema['x-component'] === 'Page';
      console.log(`   Page schema有效: ${pageValid ? '✅' : '❌'}`);
      console.log(`   是否被包装: ${pageVerifySchema.values ? '是' : '否'}`);
    } catch (error) {
      console.log(`❌ 无法验证Page schema: ${error.message}`);
    }

    // 验证Grid schema
    try {
      const gridVerifyResponse = await client.get(`/uiSchemas:getJsonSchema/${tabSchemaUid}`);
      const gridVerifySchema = gridVerifyResponse.data.data;
      console.log('\n📊 验证Grid schema:');
      console.log(JSON.stringify(gridVerifySchema, null, 2));
      
      const gridValid = gridVerifySchema.type === 'void' && 
                       gridVerifySchema['x-component'] === 'Grid' &&
                       gridVerifySchema['x-initializer'] === 'page:addBlock';
      console.log(`   Grid schema有效: ${gridValid ? '✅' : '❌'}`);
      console.log(`   组件: ${gridVerifySchema['x-component']}`);
      console.log(`   初始化器: ${gridVerifySchema['x-initializer']}`);
      console.log(`   是否被包装: ${gridVerifySchema.values ? '是' : '否'}`);
      
      if (gridValid) {
        console.log('\n🎉 使用 insert API 创建成功！现在可以创建路由了');
        
        // 第四步：创建路由
        console.log('\n📋 Step 4: 创建路由');
        
        // 创建主路由
        const routeData = {
          type: 'page',
          title: `Insert API 测试页面 ${Date.now()}`,
          schemaUid: pageSchemaUid,
          tabSchemaName: tabSchemaName,
          enableTabs: false,
          hidden: false,
          sort: 10
        };

        const routeResponse = await client.post('/desktopRoutes:create', routeData);
        const mainRoute = routeResponse.data.data;
        console.log('✅ 主路由创建成功:', JSON.stringify(mainRoute, null, 2));

        // 创建tabs子路由
        const tabsRouteData = {
          type: 'tabs',
          parentId: mainRoute.id,
          schemaUid: tabSchemaUid,
          tabSchemaName: tabSchemaName,
          hidden: true,
          sort: 1
        };

        const tabsRouteResponse = await client.post('/desktopRoutes:create', tabsRouteData);
        const tabsRoute = tabsRouteResponse.data.data;
        console.log('✅ Tabs路由创建成功:', JSON.stringify(tabsRoute, null, 2));

        // 生成访问链接
        const pageUrl = `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${pageSchemaUid}`;
        console.log(`\n🌐 访问页面: ${pageUrl}`);
        console.log('\n🎯 如果这个页面能正常显示"Add block"按钮，说明修复成功！');
        
      } else {
        console.log('\n❌ Grid schema创建失败');
      }
      
    } catch (error) {
      console.log(`❌ 无法验证Grid schema: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testInsertAPI().catch(console.error);
