#!/usr/bin/env node

/**
 * 分析现有工作页面的结构
 * 了解正确的页面创建方式
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function analyzeWorkingPage() {
  console.log('🔍 分析现有工作页面的结构\n');

  try {
    // 1. 获取所有路由
    console.log('📋 Step 1: 获取所有路由');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    console.log(`找到 ${routes.length} 个路由:`);
    routes.forEach(route => {
      console.log(`   - ${route.title} (ID: ${route.id}, Type: ${route.type}, Schema: ${route.schemaUid})`);
    });

    // 2. 找到一个工作的页面路由（选择第一个有schemaUid的页面）
    const workingPageRoute = routes.find(route =>
      route.type === 'page' &&
      route.schemaUid
    );

    if (!workingPageRoute) {
      console.log('❌ 没有找到合适的工作页面路由');
      return;
    }

    console.log(`\n📋 Step 2: 分析工作页面 "${workingPageRoute.title}"`);
    console.log(`   Route ID: ${workingPageRoute.id}`);
    console.log(`   Schema UID: ${workingPageRoute.schemaUid}`);

    // 3. 获取页面schema
    const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${workingPageRoute.schemaUid}`);
    const pageSchema = schemaResponse.data.data;

    console.log('\n📊 工作页面的完整结构:');
    console.log(JSON.stringify(pageSchema, null, 2));

    // 4. 分析结构
    console.log('\n🔍 结构分析:');
    console.log(`   类型: ${pageSchema.type}`);
    console.log(`   组件: ${pageSchema['x-component']}`);
    console.log(`   异步: ${pageSchema['x-async']}`);
    console.log(`   有属性: ${!!pageSchema.properties}`);
    
    if (pageSchema.properties) {
      console.log(`   属性数量: ${Object.keys(pageSchema.properties).length}`);
      console.log('   属性详情:');
      
      for (const [propName, propSchema] of Object.entries(pageSchema.properties)) {
        const prop = propSchema;
        console.log(`     - ${propName}:`);
        console.log(`       组件: ${prop['x-component']}`);
        console.log(`       初始化器: ${prop['x-initializer'] || 'none'}`);
        console.log(`       UID: ${prop['x-uid']}`);
        console.log(`       异步: ${prop['x-async']}`);
        
        if (prop['x-component'] === 'Grid' && prop['x-initializer'] === 'page:addBlock') {
          console.log('       ✅ 这是关键的Grid组件！');
        }
      }
    }

    // 5. 尝试复制这个结构创建新页面
    console.log('\n📋 Step 3: 尝试复制结构创建新页面');
    
    function generateUID(prefix = 'uid') {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 15);
      return `${prefix}-${timestamp}-${random}`;
    }

    const newPageUid = generateUID('page');
    const newSchemaName = `schema-${Date.now()}`;

    // 复制工作页面的结构
    const newPageSchema = {
      ...pageSchema,
      'x-uid': newPageUid,
      name: newSchemaName
    };

    // 如果有properties，需要为每个property生成新的UID
    if (newPageSchema.properties) {
      for (const [propName, propSchema] of Object.entries(newPageSchema.properties)) {
        const prop = propSchema;
        if (prop['x-uid']) {
          prop['x-uid'] = generateUID('grid');
        }
      }
    }

    console.log('🔧 创建新页面schema:', JSON.stringify(newPageSchema, null, 2));

    // 使用 uiSchemas:create 方法
    const createResponse = await client.post('/uiSchemas:create', {
      values: newPageSchema
    });

    console.log('✅ 新页面创建成功:', JSON.stringify(createResponse.data, null, 2));

    // 验证新页面结构
    const verifyResponse = await client.get(`/uiSchemas:getJsonSchema/${newPageUid}`);
    const newSchema = verifyResponse.data.data;

    console.log('\n📊 新页面验证结果:');
    console.log(`   有属性: ${!!newSchema.properties ? '✅' : '❌'}`);
    
    if (newSchema.properties) {
      let hasAddBlock = false;
      for (const [propName, propSchema] of Object.entries(newSchema.properties)) {
        const prop = propSchema;
        if (prop['x-component'] === 'Grid' && prop['x-initializer'] === 'page:addBlock') {
          hasAddBlock = true;
          break;
        }
      }
      console.log(`   Add Block 功能: ${hasAddBlock ? '✅' : '❌'}`);
    }

    // 创建路由
    const routeData = {
      type: 'page',
      title: `复制结构测试页面 ${Date.now()}`,
      schemaUid: newPageUid,
      icon: 'ExperimentOutlined',
      enableTabs: false,
      hidden: false,
      sort: 10
    };

    const routeResponse = await client.post('/desktopRoutes:create', routeData);
    console.log('\n✅ 路由创建成功:', JSON.stringify(routeResponse.data.data, null, 2));

    const pageUrl = `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${newPageUid}`;
    console.log(`\n🌐 访问新页面: ${pageUrl}`);

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行分析
analyzeWorkingPage().catch(console.error);
