#!/usr/bin/env node

/**
 * 检查最新的路由
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function checkLatestRoutes() {
  console.log('🔍 检查最新的路由\n');

  try {
    // 获取所有路由
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    // 按ID排序，显示最新的20个路由
    const sortedRoutes = routes.sort((a, b) => b.id - a.id);
    
    console.log('📋 最新的50个路由:');
    sortedRoutes.slice(0, 50).forEach(route => {
      console.log(`   - ID: ${route.id}, Title: "${route.title || 'null'}", Type: ${route.type}, Schema: ${route.schemaUid || 'null'}`);
    });

    // 查找最新的测试路由
    const latestTestRoutes = sortedRoutes.filter(route =>
      route.title && (route.title.includes('修复测试页面') || route.title.includes('直接测试页面') || route.title.includes('Insert API'))
    ).slice(0, 15);

    console.log('\n📋 最新的15个测试路由:');
    latestTestRoutes.forEach(route => {
      console.log(`   - ID: ${route.id}, Title: "${route.title}", Schema: ${route.schemaUid}`);
    });

    if (latestTestRoutes.length > 0) {
      const latestRoute = latestTestRoutes[0];
      console.log(`\n🔍 检查最新测试路由 (ID: ${latestRoute.id})`);
      
      // 查找对应的tabs子路由
      const tabsRoute = sortedRoutes.find(route => 
        route.parentId === latestRoute.id && route.type === 'tabs'
      );

      if (tabsRoute) {
        console.log(`✅ 找到tabs子路由 (ID: ${tabsRoute.id})`);
        
        // 验证schemas
        console.log('\n📋 验证schemas:');
        
        // 验证主页面schema
        try {
          const pageSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${latestRoute.schemaUid}`);
          const pageSchema = pageSchemaResponse.data.data;
          
          console.log('📊 主页面Schema:');
          if (Object.keys(pageSchema).length === 0) {
            console.log('   ❌ 空对象');
          } else {
            console.log(JSON.stringify(pageSchema, null, 2));
            
            // 检查是否被包装在values中
            let actualSchema = pageSchema;
            if (pageSchema.values && typeof pageSchema.values === 'object') {
              console.log('🔧 Schema被包装在values中');
              actualSchema = pageSchema.values;
            }
            
            const isValid = actualSchema.type === 'void' && actualSchema['x-component'] === 'Page';
            console.log(`   页面Schema有效: ${isValid ? '✅' : '❌'}`);
          }
        } catch (error) {
          console.log(`   ❌ 无法获取主页面schema: ${error.message}`);
        }

        // 验证tabs schema
        try {
          const tabsSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${tabsRoute.schemaUid}`);
          const tabsSchema = tabsSchemaResponse.data.data;
          
          console.log('\n📊 Tabs Schema:');
          if (Object.keys(tabsSchema).length === 0) {
            console.log('   ❌ 空对象');
          } else {
            console.log(JSON.stringify(tabsSchema, null, 2));
            
            // 检查是否被包装在values中
            let actualSchema = tabsSchema;
            if (tabsSchema.values && typeof tabsSchema.values === 'object') {
              console.log('🔧 Schema被包装在values中');
              actualSchema = tabsSchema.values;
            }
            
            const isValid = actualSchema.type === 'void' && 
                           actualSchema['x-component'] === 'Grid' &&
                           actualSchema['x-initializer'] === 'page:addBlock';
            console.log(`   Tabs Schema有效: ${isValid ? '✅' : '❌'}`);
            console.log(`   组件: ${actualSchema['x-component']}`);
            console.log(`   初始化器: ${actualSchema['x-initializer']}`);
          }
        } catch (error) {
          console.log(`   ❌ 无法获取tabs schema: ${error.message}`);
        }

        // 生成访问链接
        const pageUrl = `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${latestRoute.schemaUid}`;
        console.log(`\n🌐 访问页面: ${pageUrl}`);
        
      } else {
        console.log('❌ 没有找到对应的tabs子路由');
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行检查
checkLatestRoutes().catch(console.error);
