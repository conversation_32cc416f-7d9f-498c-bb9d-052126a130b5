#!/usr/bin/env node

/**
 * 验证新创建的路由是否按照成功路由的模式工作
 */

import axios from 'axios';

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function verifyNewRoute() {
  console.log('🔍 验证新创建的路由结构\n');

  try {
    // 1. 获取所有路由
    console.log('📋 Step 1: 获取所有路由');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    // 找到最新创建的路由（ID最大的）
    const testRoutes = routes
      .filter(route => route.title && route.title.includes('修复测试页面'))
      .sort((a, b) => b.id - a.id);

    console.log('所有测试路由:');
    testRoutes.forEach(route => {
      console.log(`   - ID: ${route.id}, Title: ${route.title}, Schema: ${route.schemaUid}`);
    });

    // 显示所有路由的ID，找到真正的最新路由
    console.log('\n所有路由ID（前15个）:');
    routes.sort((a, b) => b.id - a.id).slice(0, 15).forEach(route => {
      console.log(`   - ID: ${route.id}, Title: ${route.title || 'null'}, Type: ${route.type}, Schema: ${route.schemaUid || 'null'}`);
    });

    // 找到ID最大的路由（包括所有类型）
    const allRoutesSorted = routes.sort((a, b) => b.id - a.id);
    const latestTestRoute = allRoutesSorted.find(route =>
      route.title && route.title.includes('修复测试页面')
    );

    console.log(`\n🔍 最新的测试路由: ID ${latestTestRoute?.id}, Title: ${latestTestRoute?.title}`);
    const latestRoute = latestTestRoute || testRoutes[0];

    if (!latestRoute) {
      console.log('❌ 没有找到最新的测试路由');
      return;
    }

    console.log('✅ 找到最新的测试路由:');
    console.log(`   ID: ${latestRoute.id}`);
    console.log(`   Title: ${latestRoute.title}`);
    console.log(`   Schema UID: ${latestRoute.schemaUid}`);
    console.log(`   Tab Schema Name: ${latestRoute.tabSchemaName}`);

    // 2. 查找对应的tabs子路由
    console.log('\n📋 Step 2: 查找tabs子路由');
    const tabsRoute = routes.find(route => 
      route.parentId === latestRoute.id && route.type === 'tabs'
    );

    if (!tabsRoute) {
      console.log('❌ 没有找到对应的tabs子路由');
      return;
    }

    console.log('✅ 找到tabs子路由:');
    console.log(`   ID: ${tabsRoute.id}`);
    console.log(`   Parent ID: ${tabsRoute.parentId}`);
    console.log(`   Schema UID: ${tabsRoute.schemaUid}`);
    console.log(`   Tab Schema Name: ${tabsRoute.tabSchemaName}`);
    console.log(`   Hidden: ${tabsRoute.hidden}`);

    // 3. 验证主页面schema
    console.log('\n📋 Step 3: 验证主页面schema');
    try {
      const pageSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${latestRoute.schemaUid}`);
      const pageSchema = pageSchemaResponse.data.data;
      
      console.log('📊 主页面Schema:');
      console.log(JSON.stringify(pageSchema, null, 2));
      
      const isValidPageSchema = pageSchema.type === 'void' && 
                               pageSchema['x-component'] === 'Page' && 
                               pageSchema['x-async'] === false;
      
      console.log(`   页面Schema有效: ${isValidPageSchema ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`❌ 无法获取主页面schema: ${error.message}`);
    }

    // 4. 验证tabs schema（关键部分）
    console.log('\n📋 Step 4: 验证tabs schema');
    try {
      const tabsSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${tabsRoute.schemaUid}`);
      const tabsSchema = tabsSchemaResponse.data.data;
      
      console.log('📊 Tabs Schema:');
      console.log(JSON.stringify(tabsSchema, null, 2));
      
      // 处理可能被包装的schema
      let actualTabsSchema = tabsSchema;
      if (tabsSchema.values && typeof tabsSchema.values === 'object') {
        console.log('🔧 Schema被包装在values中，提取实际schema');
        actualTabsSchema = tabsSchema.values;
      }
      
      const isValidTabsSchema = actualTabsSchema.type === 'void' && 
                               actualTabsSchema['x-component'] === 'Grid' && 
                               actualTabsSchema['x-initializer'] === 'page:addBlock';
      
      console.log(`   Tabs Schema有效: ${isValidTabsSchema ? '✅' : '❌'}`);
      console.log(`   组件类型: ${actualTabsSchema['x-component']}`);
      console.log(`   初始化器: ${actualTabsSchema['x-initializer']}`);
      
      if (isValidTabsSchema) {
        console.log('\n🎉 成功！新路由结构与成功路由完全一致！');
      } else {
        console.log('\n⚠️ 新路由结构还有问题');
      }
      
    } catch (error) {
      console.log(`❌ 无法获取tabs schema: ${error.message}`);
    }

    // 5. 与成功路由对比
    console.log('\n📋 Step 5: 与成功路由对比');
    const successfulRoute = routes.find(route => 
      route.title === '修复测试页面 1755064624380'
    );

    if (successfulRoute) {
      const successfulTabsRoute = routes.find(route => 
        route.parentId === successfulRoute.id && route.type === 'tabs'
      );

      console.log('🔍 结构对比:');
      console.log(`   成功路由 - Enable Tabs: ${successfulRoute.enableTabs}`);
      console.log(`   新路由   - Enable Tabs: ${latestRoute.enableTabs}`);
      console.log(`   成功路由 - 有tabs子路由: ${!!successfulTabsRoute}`);
      console.log(`   新路由   - 有tabs子路由: ${!!tabsRoute}`);
      
      if (successfulTabsRoute && tabsRoute) {
        console.log(`   成功tabs - Hidden: ${successfulTabsRoute.hidden}`);
        console.log(`   新tabs   - Hidden: ${tabsRoute.hidden}`);
      }
    }

    // 6. 生成访问链接
    console.log('\n🌐 访问链接:');
    const pageUrl = `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${latestRoute.schemaUid}`;
    console.log(`   新页面: ${pageUrl}`);
    
    if (successfulRoute) {
      const successUrl = `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${successfulRoute.schemaUid}`;
      console.log(`   成功页面: ${successUrl}`);
    }

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行验证
verifyNewRoute().catch(console.error);
