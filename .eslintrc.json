{"env": {"node": true, "es2022": true}, "extends": ["eslint:recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "warn", "no-console": "off", "no-inner-declarations": "off", "quotes": "off", "semi": "off", "indent": "off"}, "ignorePatterns": ["dist/", "node_modules/", "scripts/"]}